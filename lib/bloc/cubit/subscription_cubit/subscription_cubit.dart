// subscription_cubit.dart

import 'dart:io' show Platform;
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/data/models/subscription/coupon_model.dart';
import 'package:busaty_parents/data/repo/subscription_repo.dart';
import 'package:busaty_parents/services/payment_service.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logger/logger.dart';
import 'package:busaty_parents/config/config_base.dart';
import 'package:busaty_parents/helper/network_serviecs.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/views/screens/home_screen/home_screen.dart';
import 'package:flutter/material.dart' as material;

part 'subscription_state.dart';

bool isPro = PaymentService.instance.isProUser;

class SubscriptionCubit extends Cubit<SubscriptionState> {
  SubscriptionCubit() : super(SubscriptionInitial());

  static SubscriptionCubit get(context) => BlocProvider.of(context);
  final _subscriptionsRepo = SubscriptionsRepo();

  CouponModel? coupon;

  Future<void> couponSubscription(
      {required String code, required context}) async {
    emit(couponLoading());
    try {
      final response = await _subscriptionsRepo.couponSubscribe(code: code);
      print("is pro: $isPro");
      if (response.status == true) {
        material.debugPrint(response.toString());
        coupon = response;
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const HomeScreen(),
            ),
            (route) => false);
        subscriptionStatus = true;
        getUserData();
        snackBarKey.currentState?.showSnackBar(snackBar);
        print("is pro: $isPro");
      } else {
        material.debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.messages,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e, stackTrace) {
      material.debugPrint("catch error at Absence repo: $e");
      material.debugPrint(stackTrace.toString());
      material.debugPrint("error $e");
    }
    emit(SubscriptionInitial());
  }

  Future<void> buySubscription(context) async {
    emit(SubscriptionLoading());
    try {
      final String productId = Platform.isIOS
          ? 'com.busaty.parent.subscription.yearly'
          : 'subscribtion_yearly';

      final ProductDetailsResponse response =
          await InAppPurchase.instance.queryProductDetails({productId});

      if (response.notFoundIDs.isNotEmpty) {
        material.debugPrint('Product not found: ${response.notFoundIDs}');
        emit(SubscriptionError('Product not found: ${response.notFoundIDs}'));
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        return;
      }

      if (response.productDetails.isEmpty) {
        material.debugPrint('No products found');
        emit(SubscriptionError('No products found'));
        return;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: response.productDetails.first,
        applicationUserName: null,
      );

      // Start the purchase
      final bool success = await InAppPurchase.instance.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        emit(SubscriptionError('Failed to start purchase'));
        return;
      }

      // The actual purchase result will be delivered via the InAppPurchase.instance.purchaseStream
      // Make sure you're listening to this stream and handling the purchase updates
    } catch (e) {
      material.debugPrint('Error during subscription purchase: $e');
      emit(SubscriptionError(e.toString()));
      SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: e.toString(),
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  void listenToPurchaseUpdates(context) {
    InAppPurchase.instance.purchaseStream.listen(
      (purchaseDetailsList) {
        handlePurchaseUpdates(purchaseDetailsList);
      },
      onError: (error) {
        material.debugPrint('Error in purchase stream: $error');
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      },
    );
  }

  void handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        emit(SubscriptionLoading());
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        emit(SubscriptionError(
            purchaseDetails.error?.message ?? 'Purchase failed'));
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // Verify the purchase with your backend
        _verifyPurchase(purchaseDetails);
      }

      if (purchaseDetails.pendingCompletePurchase) {
        InAppPurchase.instance.completePurchase(purchaseDetails);
      }
    }
  }

  Future<void> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      final response =
          await _subscriptionsRepo.verifyPurchase(purchaseDetails.purchaseID!);

      if (response.status) {
        emit(SubscriptionSuccess());
        subscriptionStatus = true;
        getUserData();

        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        emit(SubscriptionError(response.message));
      }
    } catch (e) {
      emit(SubscriptionError('Failed to verify purchase: $e'));
    }
  }

  getUserData() async {
    try {
      material.debugPrint('🔍 FETCHING USER DATA FROM API...');
      material.debugPrint('📡 API Endpoint: ${ConfigBase.baseUrl}${ConfigBase.profile}');

      var response =
          await NetworkService().get(url: ConfigBase.profile, isAuth: true);

      material.debugPrint('✅ API RESPONSE RECEIVED');
      material.debugPrint('📊 Status Code: ${response.statusCode}');
      material.debugPrint('📋 Response Headers: ${response.headers}');
      material.debugPrint('');
      material.debugPrint('🔍 === COMPLETE API RESPONSE DATA ===');
      material.debugPrint('${response.data}');
      material.debugPrint('=====================================');
      material.debugPrint('');

      // Check response structure
      material.debugPrint('🔍 ANALYZING RESPONSE STRUCTURE:');
      material.debugPrint('📝 Response type: ${response.data.runtimeType}');
      if (response.data is Map) {
        material.debugPrint('🗂️ Available keys in response: ${(response.data as Map).keys.toList()}');
      }
      material.debugPrint('');

      userEmail = response.data["email"];
      userName = response.data["name"];
      userImageUrl = response.data["logo_path"];
      userPhone = response.data["phone"];
      userAddress = response.data["address"];
      subscriptionStatus = response.data["subscription_status"] ?? false;

      material.debugPrint('👤 USER DATA EXTRACTED:');
      material.debugPrint('📧 Email: $userEmail');
      material.debugPrint('👤 Name: $userName');
      material.debugPrint('📱 Phone: $userPhone');
      material.debugPrint('🏠 Address: $userAddress');
      material.debugPrint('💳 Subscription Status: $subscriptionStatus');
      material.debugPrint('');

      // Check for subscription field (both singular and plural)
      bool hasSubscriptionField = response.data["subscription"] != null;
      bool hasSubscriptionsField = response.data["subscriptions"] != null;
      material.debugPrint('🔍 SUBSCRIPTION FIELD ANALYSIS:');
      material.debugPrint('📋 subscription field exists: $hasSubscriptionField');
      material.debugPrint('📋 subscriptions field exists: $hasSubscriptionsField');

      if (hasSubscriptionsField) {
        material.debugPrint('📋 subscriptions field type: ${response.data["subscriptions"].runtimeType}');
        if (response.data["subscriptions"] is List) {
          material.debugPrint('📊 subscriptions array length: ${(response.data["subscriptions"] as List).length}');
        }
      }
      material.debugPrint('');

      // Extract subscription details if available
      // First check for "subscriptions" array (current API format)
      if (response.data["subscriptions"] != null && response.data["subscriptions"] is List) {
        var subscriptionsArray = response.data["subscriptions"] as List;
        if (subscriptionsArray.isNotEmpty) {
          // Get the first (most recent) subscription
          var subscriptionData = subscriptionsArray.first;
          material.debugPrint('💳 === SUBSCRIPTION DATA FOUND (from subscriptions array) ===');
          material.debugPrint('$subscriptionData');
          material.debugPrint('=======================================================');
          material.debugPrint('');

          subscriptionPlanName = subscriptionData["plan_name"];
          subscriptionAmount = subscriptionData["amount"];
          subscriptionStartDate = subscriptionData["start_date"];
          subscriptionEndDate = subscriptionData["end_date"];
          subscriptionPaymentMethod = subscriptionData["payment_method"];
          subscriptionStatusText = subscriptionData["status"];

          material.debugPrint('✅ SUBSCRIPTION INFO EXTRACTED:');
          material.debugPrint('📋 Plan Name: $subscriptionPlanName');
          material.debugPrint('💰 Amount: $subscriptionAmount');
          material.debugPrint('📅 Start Date: $subscriptionStartDate');
          material.debugPrint('📅 End Date: $subscriptionEndDate');
          material.debugPrint('💳 Payment Method: $subscriptionPaymentMethod');
          material.debugPrint('📊 Status: $subscriptionStatusText');
          material.debugPrint('');

          // Validate extracted data
          material.debugPrint('🔍 DATA VALIDATION:');
          material.debugPrint('✓ Plan Name is ${subscriptionPlanName != null ? "NOT NULL" : "NULL"}');
          material.debugPrint('✓ Amount is ${subscriptionAmount != null ? "NOT NULL" : "NULL"}');
          material.debugPrint('✓ Start Date is ${subscriptionStartDate != null ? "NOT NULL" : "NULL"}');
          material.debugPrint('✓ End Date is ${subscriptionEndDate != null ? "NOT NULL" : "NULL"}');
          material.debugPrint('✓ Payment Method is ${subscriptionPaymentMethod != null ? "NOT NULL" : "NULL"}');
          material.debugPrint('✓ Status Text is ${subscriptionStatusText != null ? "NOT NULL" : "NULL"}');
        } else {
          material.debugPrint('⚠️ SUBSCRIPTIONS ARRAY IS EMPTY');
          _resetSubscriptionData();
        }
      }
      // Fallback: check for "subscription" object (legacy format)
      else if (response.data["subscription"] != null) {
        var subscriptionData = response.data["subscription"];
        material.debugPrint('💳 === SUBSCRIPTION DATA FOUND (from subscription object) ===');
        material.debugPrint('$subscriptionData');
        material.debugPrint('========================================================');
        material.debugPrint('');

        subscriptionPlanName = subscriptionData["plan_name"];
        subscriptionAmount = subscriptionData["amount"];
        subscriptionStartDate = subscriptionData["start_date"];
        subscriptionEndDate = subscriptionData["end_date"];
        subscriptionPaymentMethod = subscriptionData["payment_method"];
        subscriptionStatusText = subscriptionData["status"];

        material.debugPrint('✅ SUBSCRIPTION INFO EXTRACTED (legacy format):');
        material.debugPrint('📋 Plan Name: $subscriptionPlanName');
        material.debugPrint('💰 Amount: $subscriptionAmount');
        material.debugPrint('📅 Start Date: $subscriptionStartDate');
        material.debugPrint('📅 End Date: $subscriptionEndDate');
        material.debugPrint('💳 Payment Method: $subscriptionPaymentMethod');
        material.debugPrint('📊 Status: $subscriptionStatusText');
      } else {
        material.debugPrint('❌ NO SUBSCRIPTION DATA FOUND IN API RESPONSE');
        material.debugPrint('🔍 Checked both "subscription" and "subscriptions" fields');
        material.debugPrint('');
        _resetSubscriptionData();
      }

      material.debugPrint('🏁 USER DATA FETCH COMPLETED');
      material.debugPrint('=====================================');
    } catch (e, stackTrace) {
      material.debugPrint("❌ ERROR FETCHING USER DATA: $e");
      material.debugPrint("📋 Stack trace: $stackTrace");
    }
  }

  void _resetSubscriptionData() {
    subscriptionPlanName = null;
    subscriptionAmount = null;
    subscriptionStartDate = null;
    subscriptionEndDate = null;
    subscriptionPaymentMethod = null;
    subscriptionStatusText = null;
    material.debugPrint('🔄 SUBSCRIPTION DATA RESET TO NULL');
  }
}

// Add these states to your subscription_state.dart file if not already present
class SubscriptionLoading extends SubscriptionState {
  @override
  List<Object?> get props => [];
}

class SubscriptionSuccess extends SubscriptionState {
  @override
  List<Object?> get props => [];
}

class SubscriptionError extends SubscriptionState {
  final String message;
  const SubscriptionError(this.message);

  @override
  List<Object?> get props => [message];
}

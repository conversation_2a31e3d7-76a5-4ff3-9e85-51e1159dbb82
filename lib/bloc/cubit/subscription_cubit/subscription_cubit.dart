// subscription_cubit.dart

import 'dart:io' show Platform;
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/data/models/subscription/coupon_model.dart';
import 'package:busaty_parents/data/repo/subscription_repo.dart';
import 'package:busaty_parents/services/payment_service.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:logger/logger.dart';
import 'package:busaty_parents/config/config_base.dart';
import 'package:busaty_parents/helper/network_serviecs.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/views/screens/home_screen/home_screen.dart';
import 'package:flutter/material.dart' as material;

part 'subscription_state.dart';

bool isPro = PaymentService.instance.isProUser;

class SubscriptionCubit extends Cubit<SubscriptionState> {
  SubscriptionCubit() : super(SubscriptionInitial());

  static SubscriptionCubit get(context) => BlocProvider.of(context);
  final _subscriptionsRepo = SubscriptionsRepo();

  CouponModel? coupon;

  Future<void> couponSubscription(
      {required String code, required context}) async {
    emit(couponLoading());
    try {
      final response = await _subscriptionsRepo.couponSubscribe(code: code);
      print("is pro: $isPro");
      if (response.status == true) {
        material.debugPrint(response.toString());
        coupon = response;
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const HomeScreen(),
            ),
            (route) => false);
        subscriptionStatus = true;
        getUserData();
        snackBarKey.currentState?.showSnackBar(snackBar);
        print("is pro: $isPro");
      } else {
        material.debugPrint("ErrorState: ${response.message}");
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: response.messages,
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      }
    } catch (e, stackTrace) {
      material.debugPrint("catch error at Absence repo: $e");
      material.debugPrint(stackTrace.toString());
      material.debugPrint("error $e");
    }
    emit(SubscriptionInitial());
  }

  Future<void> buySubscription(context) async {
    emit(SubscriptionLoading());
    try {
      final String productId = Platform.isIOS
          ? 'com.busaty.parent.subscription.yearly'
          : 'subscribtion_yearly';

      final ProductDetailsResponse response =
          await InAppPurchase.instance.queryProductDetails({productId});

      if (response.notFoundIDs.isNotEmpty) {
        material.debugPrint('Product not found: ${response.notFoundIDs}');
        emit(SubscriptionError('Product not found: ${response.notFoundIDs}'));
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
        return;
      }

      if (response.productDetails.isEmpty) {
        material.debugPrint('No products found');
        emit(SubscriptionError('No products found'));
        return;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: response.productDetails.first,
        applicationUserName: null,
      );

      // Start the purchase
      final bool success = await InAppPurchase.instance.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      if (!success) {
        emit(SubscriptionError('Failed to start purchase'));
        return;
      }

      // The actual purchase result will be delivered via the InAppPurchase.instance.purchaseStream
      // Make sure you're listening to this stream and handling the purchase updates
    } catch (e) {
      material.debugPrint('Error during subscription purchase: $e');
      emit(SubscriptionError(e.toString()));
      SnackBar snackBar = SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: e.toString(),
          color: TColor.white,
          maxLine: 3,
        ),
      );
      snackBarKey.currentState?.showSnackBar(snackBar);
    }
  }

  void listenToPurchaseUpdates(context) {
    InAppPurchase.instance.purchaseStream.listen(
      (purchaseDetailsList) {
        handlePurchaseUpdates(purchaseDetailsList);
      },
      onError: (error) {
        material.debugPrint('Error in purchase stream: $error');
        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "",
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      },
    );
  }

  void handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        emit(SubscriptionLoading());
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        emit(SubscriptionError(
            purchaseDetails.error?.message ?? 'Purchase failed'));
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // Verify the purchase with your backend
        _verifyPurchase(purchaseDetails);
      }

      if (purchaseDetails.pendingCompletePurchase) {
        InAppPurchase.instance.completePurchase(purchaseDetails);
      }
    }
  }

  Future<void> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      final response =
          await _subscriptionsRepo.verifyPurchase(purchaseDetails.purchaseID!);

      if (response.status) {
        emit(SubscriptionSuccess());
        subscriptionStatus = true;
        getUserData();

        SnackBar snackBar = SnackBar(
          backgroundColor: TColor.greenSuccess,
          content: CustomText(
            text: AppStrings.subscribedSuccessfully.tr(),
            color: TColor.white,
            maxLine: 3,
          ),
        );
        snackBarKey.currentState?.showSnackBar(snackBar);
      } else {
        emit(SubscriptionError(response.message));
      }
    } catch (e) {
      emit(SubscriptionError('Failed to verify purchase: $e'));
    }
  }

  getUserData() async {
    try {
      var response =
          await NetworkService().get(url: ConfigBase.profile, isAuth: true);
      material.debugPrint('=== API Response ===');
      material.debugPrint('${response.data}');
      material.debugPrint('===================');

      userEmail = response.data["email"];
      userName = response.data["name"];
      userImageUrl = response.data["logo_path"];
      userPhone = response.data["phone"];
      userAddress = response.data["address"];
      subscriptionStatus = response.data["subscription_status"] ?? false;

      material.debugPrint('subscriptionStatus from API: $subscriptionStatus');
      material.debugPrint('subscription field exists: ${response.data["subscription"] != null}');

      // Extract subscription details if available
      if (response.data["subscription"] != null) {
        var subscriptionData = response.data["subscription"];
        material.debugPrint('=== Subscription Data ===');
        material.debugPrint('$subscriptionData');
        material.debugPrint('========================');

        subscriptionPlanName = subscriptionData["plan_name"];
        subscriptionAmount = subscriptionData["amount"];
        subscriptionStartDate = subscriptionData["start_date"];
        subscriptionEndDate = subscriptionData["end_date"];
        subscriptionPaymentMethod = subscriptionData["payment_method"];
        subscriptionStatusText = subscriptionData["status"];

        material.debugPrint('Extracted subscription info:');
        material.debugPrint('Plan: $subscriptionPlanName');
        material.debugPrint('Amount: $subscriptionAmount');
        material.debugPrint('Start: $subscriptionStartDate');
        material.debugPrint('End: $subscriptionEndDate');
        material.debugPrint('Payment: $subscriptionPaymentMethod');
        material.debugPrint('Status: $subscriptionStatusText');
      } else {
        material.debugPrint('No subscription data found in API response');
        // Reset subscription details if no data
        subscriptionPlanName = null;
        subscriptionAmount = null;
        subscriptionStartDate = null;
        subscriptionEndDate = null;
        subscriptionPaymentMethod = null;
        subscriptionStatusText = null;
      }
    } catch (e, stackTrace) {
      material.debugPrint("catch error at profile fetch: $e");
      material.debugPrint(stackTrace.toString());
    }
  }
}

// Add these states to your subscription_state.dart file if not already present
class SubscriptionLoading extends SubscriptionState {
  @override
  List<Object?> get props => [];
}

class SubscriptionSuccess extends SubscriptionState {
  @override
  List<Object?> get props => [];
}

class SubscriptionError extends SubscriptionState {
  final String message;
  const SubscriptionError(this.message);

  @override
  List<Object?> get props => [message];
}

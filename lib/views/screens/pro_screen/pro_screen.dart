// ignore_for_file: use_build_context_synchronously

import 'dart:io'; // Import to check platform
import 'package:busaty_parents/bloc/cubit/register_cubit/register_cubit.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logger/logger.dart';
import 'package:busaty_parents/utils/sized_box.dart';

import '../../../bloc/cubit/subscription_cubit/subscription_cubit.dart';
import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../services/payment_service.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/custom_background_image.dart';
import '../../custom_widgets/custom_button.dart';
import '../../custom_widgets/custom_form_field_border.dart';
import '../../custom_widgets/custom_text.dart';

class PROScreen extends StatefulWidget {
  static const String routeName = PathRouteName.proScreen;

  const PROScreen({super.key});

  @override
  State<PROScreen> createState() => _PROScreenState();
}

class _PROScreenState extends State<PROScreen> {
  final formKey = GlobalKey<FormState>();
  TextEditingController couponController = TextEditingController();
  bool isLoading = false;
  @override
  void initState() {
    setState(() {
      isLoading = true;
      print(isLoading);
    });
    fetchUserStatus();
    // Fetch user data including subscription information
    _fetchUserData();
    super.initState();
    PaymentService.instance.initConnection().then((_) {
      Logger()
          .e('Products loaded: ${PaymentService.instance.allProducts.length}');
      setState(() {
        isLoading = false;
        print("$isLoading + =================k");
      });
    });
  }

  @override
  void dispose() {
    couponController.dispose();
    super.dispose();
  }

  Future<void> fetchUserStatus() async {
    // Assuming you have a default name or you can pass the name as needed
    String defaultName = "parents";
    await context.read<RegisterCubit>().getUserStatus(defaultName);
    // Handle the status as needed
    debugPrint("User Status: ${context.read<RegisterCubit>().getStatus}");
    setState(() {});
  }

  Future<void> _fetchUserData() async {
    try {
      await SubscriptionCubit.get(context).getUserData();
      if (mounted) {
        setState(() {
          // Update UI after fetching subscription data
        });
      }
    } catch (e) {
      debugPrint("Error fetching user data: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    Logger().e('Platform is iOS: ${Platform.isIOS}');

    return Scaffold(
      body: CustomBackgroundImage(
        child: isLoading == true
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  children: [
                    CustomAppBar(
                      height: 120.h,
                      titleWidget: CustomText(
                        text: AppStrings.pro.tr(),
                        fontSize: 18,
                        textAlign: TextAlign.center,
                        fontW: FontWeight.w600,
                        color: TColor.white,
                      ),
                      leftWidget: context.locale.toString() == "ar"
                          ? InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: SvgPicture.asset(AppAssets.arrowBack),
                            )
                          : InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: SvgPicture.asset(
                                AppAssets.forwardArrow,
                                colorFilter: const ColorFilter.mode(
                                    TColor.white, BlendMode.srcIn),
                                width: 20.w,
                                height: 20.w,
                              ),
                            ),
                    ),
                    20.verticalSpace,
                    Container(
                      padding: EdgeInsetsDirectional.symmetric(
                          horizontal: 16.w, vertical: 16.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.r),
                        color: TColor.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 3,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      width: 354.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: CustomText(
                              text: AppStrings.benefitBusatyBro.tr(),
                              color: TColor.mainColor,
                              fontW: FontWeight.w700,
                              fontSize: 20,
                            ),
                          ),
                          10.verticalSpace,
                          CustomText(
                            text: AppStrings.withoutAds.tr(),
                            color: TColor.tabColors,
                            fontW: FontWeight.w700,
                            fontSize: 16,
                            maxLine: 4,
                          ),
                          const Sbox(h: 5),
                          CustomText(
                            text: AppStrings.trackingSonInMoment.tr(),
                            color: TColor.tabColors,
                            fontW: FontWeight.w700,
                            fontSize: 16,
                            maxLine: 4,
                          ),
                          const Sbox(h: 5),
                          CustomText(
                            text: AppStrings.tackingBuysInMoment.tr(),
                            color: TColor.tabColors,
                            fontW: FontWeight.w700,
                            fontSize: 16,
                            maxLine: 4,
                          ),
                        ],
                      ),
                    ),
                    40.verticalSpace,
                    // Subscription Information Section - Show when user is subscribed
                    if (subscriptionStatus == true)
                      Container(
                        padding: EdgeInsetsDirectional.symmetric(
                            horizontal: 16.w, vertical: 16.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30.r),
                          color: TColor.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 3,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        width: 354.w,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: CustomText(
                                text: "معلومات الاشتراك", // Subscription Information
                                color: TColor.mainColor,
                                fontW: FontWeight.w700,
                                fontSize: 20,
                              ),
                            ),
                            20.verticalSpace,
                            // Debug information and refresh button
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CustomText(
                                        text: "Debug: subscriptionStatus = $subscriptionStatus",
                                        color: TColor.redAccent,
                                        fontSize: 12,
                                      ),
                                      CustomText(
                                        text: "Debug: subscriptionPlanName = $subscriptionPlanName",
                                        color: TColor.redAccent,
                                        fontSize: 12,
                                      ),
                                    ],
                                  ),
                                ),
                                ElevatedButton(
                                  onPressed: () async {
                                    debugPrint("🔄 MANUAL REFRESH TRIGGERED");
                                    await _fetchUserData();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: TColor.mainColor,
                                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                                  ),
                                  child: CustomText(
                                    text: "🔄 Refresh",
                                    color: TColor.white,
                                    fontSize: 10,
                                  ),
                                ),
                              ],
                            ),
                            10.verticalSpace,
                            if (subscriptionPlanName != null)
                              _buildSubscriptionInfoRow(
                                "نوع الخطة:", // Plan Type
                                subscriptionPlanName!,
                              ),
                            if (subscriptionAmount != null)
                              _buildSubscriptionInfoRow(
                                "المبلغ:", // Amount
                                "$subscriptionAmount",
                              ),
                            if (subscriptionStartDate != null)
                              _buildSubscriptionInfoRow(
                                "تاريخ البداية:", // Start Date
                                _formatDate(subscriptionStartDate!),
                              ),
                            if (subscriptionEndDate != null)
                              _buildSubscriptionInfoRow(
                                "تاريخ الانتهاء:", // End Date
                                _formatDate(subscriptionEndDate!),
                              ),
                            if (subscriptionPaymentMethod != null)
                              _buildSubscriptionInfoRow(
                                "طريقة الدفع:", // Payment Method
                                subscriptionPaymentMethod!,
                              ),
                            if (subscriptionStatusText != null)
                              _buildSubscriptionInfoRow(
                                "حالة الاشتراك:", // Subscription Status
                                subscriptionStatusText!,
                              ),
                            // Show message if no subscription data available
                            if (subscriptionPlanName == null &&
                                subscriptionAmount == null &&
                                subscriptionStartDate == null &&
                                subscriptionEndDate == null &&
                                subscriptionPaymentMethod == null &&
                                subscriptionStatusText == null)
                              CustomText(
                                text: "لا توجد معلومات اشتراك متاحة من الـ API",
                                color: TColor.redAccent,
                                fontSize: 14,
                                textAlign: TextAlign.center,
                              ),
                          ],
                        ),
                      ),
                    if (subscriptionStatus == true) 20.verticalSpace,
                    Container(
                      padding: EdgeInsetsDirectional.symmetric(
                          horizontal: 16.w, vertical: 16.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.r),
                        color: TColor.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 3,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      width: 354.w,
                      child: BlocBuilder<SubscriptionCubit, SubscriptionState>(
                        builder: (context, state) {
                          return Column(
                            children: [
                              CustomText(
                                text: subscriptionStatus == false &&
                                        context
                                                .read<RegisterCubit>()
                                                .getStatus ==
                                            true
                                    ? AppStrings.subscribeWithCoupon.tr()
                                    : AppStrings.alreadySubscribed.tr(),
                                color: TColor.mainColor,
                                fontW: FontWeight.w700,
                                fontSize: 20,
                              ),
                              if (subscriptionStatus == false) ...[
                                10.verticalSpace,
                                Form(
                                  key: formKey,
                                  child: CustomFormFieldWithBorder(
                                    readOnly: isPro == true,
                                    titleColor: TColor.mainColor,
                                    controller: couponController,
                                    validation:
                                        '${AppStrings.coupon.tr()}${AppStrings.isRequired.tr()}',
                                    contentPaddingVertical: 0,
                                    inputType: TextInputType.name,
                                    prefix: const Icon(
                                      Icons.local_offer,
                                      color: TColor.iconInputColor,
                                    ),
                                    formFieldWidth: 307.w,
                                    hintText: AppStrings.enterCoupon.tr(),
                                    borderColor: TColor.fillFormFieldB,
                                    fillColor: TColor.fillFormFieldB,
                                    radiusNumber: 15.0,
                                  ),
                                ),
                                30.verticalSpace,
                                CustomButton(
                                  loading: state is couponLoading,
                                  text: AppStrings.subscribe.tr(),
                                  fontSize: 22,
                                  onTap: () {
                                    if (formKey.currentState!.validate()) {
                                      SubscriptionCubit.get(context)
                                          .couponSubscription(
                                              code: couponController.text,
                                              context: context);
                                    }
                                  },
                                  radius: 15.sp,
                                  width: 307.w,
                                  height: 53,
                                  borderColor: TColor.mainColor,
                                  bgColor: TColor.mainColor,
                                ),
                                10.verticalSpace
                              ],
                            ],
                          );
                        },
                      ),
                    ),
                    subscriptionStatus == false
                        ? 20.verticalSpace
                        : const SizedBox(),
                    if (subscriptionStatus == false)
                      Container(
                        padding: EdgeInsetsDirectional.symmetric(
                            horizontal: 16.w, vertical: 16.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30.r),
                          color: TColor.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 3,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        width: 354.w,
                        child: Column(
                          children: [
                            // Updated title to clearly indicate it's a subscription option.
                            CustomText(
                              text:
                                  "subscription", // Ensure your localization string is updated
                              color: TColor.mainColor,
                              fontW: FontWeight.w700,
                              fontSize: 20,
                            ),
                            10.verticalSpace,
                            PaymentService.instance.allProducts.isNotEmpty
                                ? ListView.separated(
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      final product = PaymentService
                                          .instance.allProducts[index];
                                      String? title = product.title;
                                      if (title != null) {
                                        // Remove any occurrence of "Apple Pay" from the product title.
                                        title = title
                                            .replaceAll("Apple Pay", "")
                                            .trim();
                                        if (title.contains("(")) {
                                          title = title.split('(')[0].trim();
                                        }
                                      }
                                      return CustomButton(
                                        text:
                                            '$title ${product.price} ${product.currency}',
                                        fontSize: 14,
                                        onTap: () async {
                                          await PaymentService.instance
                                              .buyProduct(product);
                                        },
                                        radius: 15.sp,
                                        width: 307.w,
                                        height: 53,
                                        borderColor: TColor.mainColor,
                                        bgColor: TColor.mainColor,
                                      );
                                    },
                                    separatorBuilder: (context, index) =>
                                        10.verticalSpace,
                                    itemCount: PaymentService
                                        .instance.allProducts.length,
                                  )
                                : CustomText(
                                    text: AppStrings.notFound.tr(),
                                    fontSize: 18.sp,
                                  ),
                            10.verticalSpace
                          ],
                        ),
                      )
                    else
                      const SizedBox(),
                  ],
                ),
              ),
      ),
    );
  }

  // Helper method to build subscription information rows
  Widget _buildSubscriptionInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: CustomText(
              text: label,
              color: TColor.tabColors,
              fontW: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          8.horizontalSpace,
          Expanded(
            flex: 3,
            child: CustomText(
              text: value,
              color: TColor.black,
              fontW: FontWeight.w500,
              fontSize: 14,
              maxLine: 2,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date strings
  String _formatDate(String dateString) {
    try {
      DateTime date = DateTime.parse(dateString);
      return "${date.day}/${date.month}/${date.year}";
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }
}
